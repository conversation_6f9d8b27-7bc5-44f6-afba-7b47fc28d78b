/**
 * 主样式文件
 * 引入设计令牌、主题、组件样式和工具类
 */

/* 引入样式系统 */
@import './design-tokens.css';
@import './theme.css';
@import './components.css';
@import './utilities.css';

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

html, body {
  height: 100%;
  width: 100%;
}

#app {
  height: 100%;
  width: 100%;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  transition: var(--transition-color);
}

a:hover {
  color: var(--color-primary-light-1);
}

a:active {
  color: var(--color-primary-dark-1);
}

a:focus {
  outline: 2px solid var(--color-primary-light-8);
  outline-offset: 2px;
}

/* 按钮重置 */
button {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: 0;
  cursor: pointer;
  outline: none;
}

/* 表单元素重置 */
input,
textarea,
select {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 列表样式 */
ul, ol {
  list-style: none;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-small);
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-extra-large); }
h2 { font-size: var(--font-size-large); }
h3 { font-size: var(--font-size-medium); }
h4 { font-size: var(--font-size-base); }
h5 { font-size: var(--font-size-small); }
h6 { font-size: var(--font-size-extra-small); }

/* 段落样式 */
p {
  margin: 0 0 var(--compact-spacing-lg) 0;
  color: var(--color-text-regular);
}

/* 代码样式 */
code,
kbd,
pre,
samp {
  font-family: var(--font-family-mono);
  font-size: 1em;
}

pre {
  margin: 0;
  overflow: auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-fill-light);
  border-radius: var(--border-radius-small);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: var(--border-radius-small);
  transition: var(--transition-color);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-darker);
}

::-webkit-scrollbar-corner {
  background: var(--color-fill-light);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-dark) var(--color-fill-light);
}

/* 选择文本样式 */
::selection {
  background: var(--color-primary-light-8);
  color: var(--color-primary-dark-1);
}

::-moz-selection {
  background: var(--color-primary-light-8);
  color: var(--color-primary-dark-1);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 禁用状态 */
[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 隐藏元素 */
[hidden] {
  display: none !important;
}

/* 无障碍 - 屏幕阅读器专用 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}