<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑任务' : '创建任务'"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="task-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="task-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="form-section__title">基本信息</div>

        <el-form-item label="任务名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入任务名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入任务描述"
            :rows="3"
            resize="none"
          />
        </el-form-item>

        <el-form-item label="任务类型" prop="task_type">
          <el-select
            v-model="formData.task_type"
            placeholder="选择任务类型"
            style="width: 100%"
          >
            <el-option
              v-for="type in taskTypes"
              :key="type.value"
              :value="type.value"
              :label="type.label"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="关联应用" prop="app_id">
          <el-select
            v-model="formData.app_id"
            placeholder="选择关联应用（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="app in apps"
              :key="app.id"
              :value="app.id"
              :label="app.name"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 执行配置 -->
      <div class="form-section">
        <div class="form-section__title">执行配置</div>

        <el-form-item label="执行命令" prop="command">
          <el-input
            v-model="formData.command"
            type="textarea"
            placeholder="请输入要执行的命令"
            :rows="3"
            resize="none"
          />
        </el-form-item>

        <el-form-item label="工作目录" prop="working_directory">
          <el-input
            v-model="formData.working_directory"
            placeholder="请输入工作目录路径（可选）"
            clearable
          />
        </el-form-item>

        <el-form-item label="任务参数" prop="parameters">
          <div class="parameters-editor">
            <div
              v-for="(param, index) in parametersList"
              :key="index"
              class="parameter-item"
            >
              <el-input
                v-model="param.key"
                placeholder="参数名"
                class="param-key"
              />
              <el-input
                v-model="param.value"
                placeholder="参数值"
                class="param-value"
              />
              <el-button
                text
                type="danger"
                @click="removeParameter(index)"
                class="param-remove"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-button type="primary" plain @click="addParameter" class="add-param-btn">
              <el-icon><Plus /></el-icon>
              添加参数
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="环境变量" prop="environment_vars">
          <div class="env-vars-editor">
            <div
              v-for="(env, index) in envVarsList"
              :key="index"
              class="env-var-item"
            >
              <el-input
                v-model="env.key"
                placeholder="变量名"
                class="env-key"
              />
              <el-input
                v-model="env.value"
                placeholder="变量值"
                class="env-value"
              />
              <el-button
                text
                type="danger"
                @click="removeEnvVar(index)"
                class="env-remove"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-button type="primary" plain @click="addEnvVar" class="add-env-btn">
              <el-icon><Plus /></el-icon>
              添加环境变量
            </el-button>
          </div>
        </el-form-item>
      </div>

      <!-- 调度配置 -->
      <div class="form-section">
        <div class="form-section__title">调度配置</div>

        <el-form-item label="调度类型" prop="schedule_type">
          <el-radio-group v-model="formData.schedule_type">
            <el-radio value="manual">手动执行</el-radio>
            <el-radio value="cron">Cron调度</el-radio>
            <el-radio value="interval">间隔调度</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="formData.schedule_type === 'cron'"
          label="Cron表达式"
          prop="cron_expression"
        >
          <el-input
            v-model="formData.cron_expression"
            placeholder="例如: 0 0 * * * (每天午夜执行)"
            @blur="validateCronExpression"
            clearable
          />
          <div v-if="cronValidation.message" class="cron-validation">
            <el-alert
              :type="cronValidation.valid ? 'success' : 'error'"
              :title="cronValidation.message"
              show-icon
              :closable="false"
              class="mt-sm"
            />
            <div v-if="cronValidation.valid && cronValidation.nextRuns.length > 0" class="next-runs">
              <p class="next-runs-title">接下来的执行时间:</p>
              <ul class="next-runs-list">
                <li v-for="time in cronValidation.nextRuns" :key="time">
                  {{ formatDateTime(time) }}
                </li>
              </ul>
            </div>
          </div>
        </el-form-item>

        <el-form-item
          v-if="formData.schedule_type === 'interval'"
          label="执行间隔"
          prop="interval_seconds"
        >
          <div class="interval-input">
            <el-input-number
              v-model="formData.interval_seconds"
              :min="1"
              :max="86400"
              placeholder="间隔秒数"
              controls-position="right"
            />
            <span class="interval-unit">秒</span>
          </div>
        </el-form-item>
      </div>

      <!-- 控制配置 -->
      <div class="form-section">
        <div class="form-section__title">控制配置</div>

        <el-form-item label="任务状态" prop="is_active">
          <el-switch
            v-model="formData.is_active"
            active-text="激活"
            inactive-text="停用"
          />
        </el-form-item>

        <el-form-item label="最大重试次数" prop="max_retries">
          <el-input-number
            v-model="formData.max_retries"
            :min="0"
            :max="10"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item label="超时时间" prop="timeout_seconds">
          <div class="timeout-input">
            <el-input-number
              v-model="formData.timeout_seconds"
              :min="1"
              :max="86400"
              controls-position="right"
            />
            <span class="timeout-unit">秒</span>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { taskApi } from '@/api/tasks'
import { Task, TaskCreate, TaskUpdate, TaskFormData } from '@/types/task'
import { formatDateTime } from '@/utils/date'

interface Props {
  visible: boolean
  task?: Task | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const formRef = ref()
const taskTypes = ref<Array<{ label: string; value: string }>>([])
const apps = ref<Array<{ id: number; name: string }>>([])

// 表单数据
const formData = reactive<TaskFormData>({
  name: '',
  description: '',
  task_type: '',
  app_id: undefined,
  command: '',
  parameters: {},
  working_directory: '',
  environment_vars: {},
  schedule_type: 'manual',
  cron_expression: '',
  interval_seconds: undefined,
  is_active: true,
  max_retries: 0,
  timeout_seconds: 3600
})

// 参数列表（用于编辑器）
const parametersList = ref<Array<{ key: string; value: string }>>([])
const envVarsList = ref<Array<{ key: string; value: string }>>([])

// Cron表达式验证
const cronValidation = reactive({
  valid: false,
  message: '',
  nextRuns: [] as string[]
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.task)

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 100, message: '任务名称长度为1-100个字符', trigger: 'blur' }
  ],
  task_type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  command: [
    { required: true, message: '请输入执行命令', trigger: 'blur' }
  ],
  cron_expression: [
    {
      validator: (_: any, value: string) => {
        if (formData.schedule_type === 'cron' && !value) {
          return Promise.reject('请输入Cron表达式')
        }
        if (formData.schedule_type === 'cron' && value && !cronValidation.valid) {
          return Promise.reject('Cron表达式格式不正确')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  interval_seconds: [
    {
      validator: (_: any, value: number) => {
        if (formData.schedule_type === 'interval' && !value) {
          return Promise.reject('请输入执行间隔')
        }
        if (formData.schedule_type === 'interval' && value && (value < 1 || value > 86400)) {
          return Promise.reject('执行间隔必须在1-86400秒之间')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const loadTaskTypes = async () => {
  try {
    const res = await taskApi.getTaskTypes()
    taskTypes.value = res.data.task_types
  } catch (error) {
    console.error('加载任务类型失败:', error)
  }
}

const loadApps = async () => {
  try {
    // TODO: 实现获取应用列表的API
    // const res = await appApi.getApps()
    // apps.value = res.data
    apps.value = [] // 临时空数组
  } catch (error) {
    console.error('加载应用列表失败:', error)
  }
}

const initForm = () => {
  if (props.task) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      name: props.task.name,
      description: props.task.description || '',
      task_type: props.task.task_type,
      app_id: props.task.app_id,
      command: props.task.command || '',
      parameters: props.task.parameters || {},
      working_directory: props.task.working_directory || '',
      environment_vars: props.task.environment_vars || {},
      schedule_type: props.task.schedule_type,
      cron_expression: props.task.cron_expression || '',
      interval_seconds: props.task.interval_seconds,
      is_active: props.task.is_active,
      max_retries: props.task.max_retries,
      timeout_seconds: props.task.timeout_seconds
    })
  } else {
    // 创建模式，重置表单
    Object.assign(formData, {
      name: '',
      description: '',
      task_type: '',
      app_id: undefined,
      command: '',
      parameters: {},
      working_directory: '',
      environment_vars: {},
      schedule_type: 'manual',
      cron_expression: '',
      interval_seconds: undefined,
      is_active: true,
      max_retries: 0,
      timeout_seconds: 3600
    })
  }

  // 转换参数和环境变量为列表格式
  parametersList.value = Object.entries(formData.parameters).map(([key, value]) => ({
    key,
    value: String(value)
  }))
  
  envVarsList.value = Object.entries(formData.environment_vars).map(([key, value]) => ({
    key,
    value
  }))

  // 清除验证状态
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const addParameter = () => {
  parametersList.value.push({ key: '', value: '' })
}

const removeParameter = (index: number) => {
  parametersList.value.splice(index, 1)
}

const addEnvVar = () => {
  envVarsList.value.push({ key: '', value: '' })
}

const removeEnvVar = (index: number) => {
  envVarsList.value.splice(index, 1)
}

const validateCronExpression = async () => {
  if (!formData.cron_expression) {
    cronValidation.valid = false
    cronValidation.message = ''
    cronValidation.nextRuns = []
    return
  }

  try {
    const res = await taskApi.validateCronExpression(formData.cron_expression)
    cronValidation.valid = res.data.valid
    cronValidation.message = res.data.message
    cronValidation.nextRuns = res.data.next_runs
  } catch (error) {
    cronValidation.valid = false
    cronValidation.message = 'Cron表达式验证失败'
    cronValidation.nextRuns = []
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true

    // 转换参数和环境变量为对象格式
    const parameters: Record<string, any> = {}
    parametersList.value.forEach(param => {
      if (param.key && param.value) {
        parameters[param.key] = param.value
      }
    })

    const environment_vars: Record<string, string> = {}
    envVarsList.value.forEach(env => {
      if (env.key && env.value) {
        environment_vars[env.key] = env.value
      }
    })

    const submitData = {
      ...formData,
      parameters: Object.keys(parameters).length > 0 ? parameters : undefined,
      environment_vars: Object.keys(environment_vars).length > 0 ? environment_vars : undefined
    }

    // 清理不需要的字段
    if (submitData.schedule_type !== 'cron') {
      delete submitData.cron_expression
    }
    if (submitData.schedule_type !== 'interval') {
      delete submitData.interval_seconds
    }

    if (isEdit.value) {
      await taskApi.updateTask(props.task!.id, submitData as TaskUpdate)
      ElMessage.success('任务更新成功')
    } else {
      await taskApi.createTask(submitData as TaskCreate)
      ElMessage.success('任务创建成功')
    }

    visible.value = false
    emit('success')
  } catch (error: any) {
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      initForm()
      loadTaskTypes()
      loadApps()
    }
  }
)

// 监听Cron表达式变化
watch(
  () => formData.cron_expression,
  (newExpression) => {
    if (formData.schedule_type === 'cron' && newExpression) {
      validateCronExpression()
    } else {
      cronValidation.valid = false
      cronValidation.message = ''
      cronValidation.nextRuns = []
    }
  }
)
</script>

<style scoped>
.task-form-dialog {
  --form-label-width: 120px;
}

.task-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: var(--compact-spacing-md);
}

.form-section {
  margin-bottom: var(--compact-spacing-xl);
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section__title {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0 0 var(--compact-spacing-lg) 0;
  padding-bottom: var(--compact-spacing-md);
  border-bottom: var(--flat-border);
}

.parameters-editor,
.env-vars-editor {
  display: flex;
  flex-direction: column;
  gap: var(--compact-spacing-md);
  border: var(--flat-border);
  border-radius: var(--flat-border-radius-large);
  padding: var(--compact-spacing-lg);
  background: var(--color-fill-lighter);
}

.parameter-item,
.env-var-item {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
}

.param-key,
.env-key {
  flex: 2;
}

.param-value,
.env-value {
  flex: 3;
}

.param-remove,
.env-remove {
  flex-shrink: 0;
}

.add-param-btn,
.add-env-btn {
  margin-top: var(--compact-spacing-sm);
}

.cron-validation {
  margin-top: var(--compact-spacing-md);
}

.next-runs-title {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: var(--compact-spacing-md) 0 var(--compact-spacing-sm) 0;
}

.next-runs-list {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  padding-left: var(--compact-spacing-xl);
  margin: 0;
}

.next-runs-list li {
  margin-bottom: var(--compact-spacing-xs);
}

.interval-input,
.timeout-input {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
}

.interval-unit,
.timeout-unit {
  color: var(--color-text-regular);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--compact-spacing-md);
  padding-top: var(--compact-spacing-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-form-dialog {
    width: 95% !important;
    max-width: 600px;
  }

  .task-form :deep(.el-form-item__label) {
    float: none;
    display: block;
    text-align: left;
    padding: 0 0 var(--compact-spacing-xs) 0;
  }

  .task-form :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
</style>