/**
 * 工具类样式
 * 提供常用的CSS工具类，支持快速开发
 */

/* ========== 布局工具类 ========== */

/* Flexbox 布局 */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

/* Flex 对齐 */
.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

/* 常用组合 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* Flex 增长和收缩 */
.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

/* ========== 间距工具类 ========== */

/* Margin */
.m-0 { margin: 0; }
.m-xs { margin: var(--compact-spacing-xs); }
.m-sm { margin: var(--compact-spacing-sm); }
.m-md { margin: var(--compact-spacing-md); }
.m-lg { margin: var(--compact-spacing-lg); }
.m-xl { margin: var(--compact-spacing-xl); }

.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: var(--compact-spacing-xs); margin-right: var(--compact-spacing-xs); }
.mx-sm { margin-left: var(--compact-spacing-sm); margin-right: var(--compact-spacing-sm); }
.mx-md { margin-left: var(--compact-spacing-md); margin-right: var(--compact-spacing-md); }
.mx-lg { margin-left: var(--compact-spacing-lg); margin-right: var(--compact-spacing-lg); }
.mx-xl { margin-left: var(--compact-spacing-xl); margin-right: var(--compact-spacing-xl); }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: var(--compact-spacing-xs); margin-bottom: var(--compact-spacing-xs); }
.my-sm { margin-top: var(--compact-spacing-sm); margin-bottom: var(--compact-spacing-sm); }
.my-md { margin-top: var(--compact-spacing-md); margin-bottom: var(--compact-spacing-md); }
.my-lg { margin-top: var(--compact-spacing-lg); margin-bottom: var(--compact-spacing-lg); }
.my-xl { margin-top: var(--compact-spacing-xl); margin-bottom: var(--compact-spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--compact-spacing-xs); }
.mt-sm { margin-top: var(--compact-spacing-sm); }
.mt-md { margin-top: var(--compact-spacing-md); }
.mt-lg { margin-top: var(--compact-spacing-lg); }
.mt-xl { margin-top: var(--compact-spacing-xl); }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: var(--compact-spacing-xs); }
.mr-sm { margin-right: var(--compact-spacing-sm); }
.mr-md { margin-right: var(--compact-spacing-md); }
.mr-lg { margin-right: var(--compact-spacing-lg); }
.mr-xl { margin-right: var(--compact-spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--compact-spacing-xs); }
.mb-sm { margin-bottom: var(--compact-spacing-sm); }
.mb-md { margin-bottom: var(--compact-spacing-md); }
.mb-lg { margin-bottom: var(--compact-spacing-lg); }
.mb-xl { margin-bottom: var(--compact-spacing-xl); }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: var(--compact-spacing-xs); }
.ml-sm { margin-left: var(--compact-spacing-sm); }
.ml-md { margin-left: var(--compact-spacing-md); }
.ml-lg { margin-left: var(--compact-spacing-lg); }
.ml-xl { margin-left: var(--compact-spacing-xl); }

/* Padding */
.p-0 { padding: 0; }
.p-xs { padding: var(--compact-spacing-xs); }
.p-sm { padding: var(--compact-spacing-sm); }
.p-md { padding: var(--compact-spacing-md); }
.p-lg { padding: var(--compact-spacing-lg); }
.p-xl { padding: var(--compact-spacing-xl); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: var(--compact-spacing-xs); padding-right: var(--compact-spacing-xs); }
.px-sm { padding-left: var(--compact-spacing-sm); padding-right: var(--compact-spacing-sm); }
.px-md { padding-left: var(--compact-spacing-md); padding-right: var(--compact-spacing-md); }
.px-lg { padding-left: var(--compact-spacing-lg); padding-right: var(--compact-spacing-lg); }
.px-xl { padding-left: var(--compact-spacing-xl); padding-right: var(--compact-spacing-xl); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: var(--compact-spacing-xs); padding-bottom: var(--compact-spacing-xs); }
.py-sm { padding-top: var(--compact-spacing-sm); padding-bottom: var(--compact-spacing-sm); }
.py-md { padding-top: var(--compact-spacing-md); padding-bottom: var(--compact-spacing-md); }
.py-lg { padding-top: var(--compact-spacing-lg); padding-bottom: var(--compact-spacing-lg); }
.py-xl { padding-top: var(--compact-spacing-xl); padding-bottom: var(--compact-spacing-xl); }

.pt-0 { padding-top: 0; }
.pt-xs { padding-top: var(--compact-spacing-xs); }
.pt-sm { padding-top: var(--compact-spacing-sm); }
.pt-md { padding-top: var(--compact-spacing-md); }
.pt-lg { padding-top: var(--compact-spacing-lg); }
.pt-xl { padding-top: var(--compact-spacing-xl); }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: var(--compact-spacing-xs); }
.pr-sm { padding-right: var(--compact-spacing-sm); }
.pr-md { padding-right: var(--compact-spacing-md); }
.pr-lg { padding-right: var(--compact-spacing-lg); }
.pr-xl { padding-right: var(--compact-spacing-xl); }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: var(--compact-spacing-xs); }
.pb-sm { padding-bottom: var(--compact-spacing-sm); }
.pb-md { padding-bottom: var(--compact-spacing-md); }
.pb-lg { padding-bottom: var(--compact-spacing-lg); }
.pb-xl { padding-bottom: var(--compact-spacing-xl); }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: var(--compact-spacing-xs); }
.pl-sm { padding-left: var(--compact-spacing-sm); }
.pl-md { padding-left: var(--compact-spacing-md); }
.pl-lg { padding-left: var(--compact-spacing-lg); }
.pl-xl { padding-left: var(--compact-spacing-xl); }

/* ========== 尺寸工具类 ========== */

/* Width */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-screen { width: 100vw; }

/* Height */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }
.h-screen { height: 100vh; }

/* Min/Max Width */
.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.max-w-full { max-width: 100%; }
.max-w-none { max-width: none; }

/* Min/Max Height */
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }

/* ========== 文本工具类 ========== */

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Font Weight */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-bolder { font-weight: var(--font-weight-bolder); }

/* Font Size */
.text-xs { font-size: var(--font-size-extra-small); }
.text-sm { font-size: var(--font-size-small); }
.text-base { font-size: var(--font-size-base); }
.text-md { font-size: var(--font-size-medium); }
.text-lg { font-size: var(--font-size-large); }
.text-xl { font-size: var(--font-size-extra-large); }

/* Text Color */
.text-primary { color: var(--color-text-primary); }
.text-regular { color: var(--color-text-regular); }
.text-secondary { color: var(--color-text-secondary); }
.text-placeholder { color: var(--color-text-placeholder); }
.text-disabled { color: var(--color-text-disabled); }

.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-danger { color: var(--color-danger); }
.text-info { color: var(--color-info); }

/* Text Decoration */
.underline { text-decoration: underline; }
.no-underline { text-decoration: none; }
.line-through { text-decoration: line-through; }

/* Text Transform */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* Text Overflow */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

/* ========== 显示工具类 ========== */

.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* Visibility */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* ========== 定位工具类 ========== */

.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* ========== 边框工具类 ========== */

.border { border: var(--flat-border); }
.border-0 { border: 0; }
.border-t { border-top: var(--flat-border); }
.border-r { border-right: var(--flat-border); }
.border-b { border-bottom: var(--flat-border); }
.border-l { border-left: var(--flat-border); }

/* Border Radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--border-radius-small); }
.rounded { border-radius: var(--flat-border-radius); }
.rounded-lg { border-radius: var(--flat-border-radius-large); }
.rounded-full { border-radius: var(--border-radius-circle); }

/* ========== 阴影工具类 ========== */

.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--flat-shadow-light); }
.shadow { box-shadow: var(--flat-shadow-base); }
.shadow-lg { box-shadow: var(--flat-shadow-hover); }

/* ========== 背景工具类 ========== */

.bg-transparent { background-color: transparent; }
.bg-white { background-color: #ffffff; }
.bg-page { background-color: var(--color-bg-page); }
.bg-overlay { background-color: var(--color-bg-overlay); }

/* ========== 过渡动画工具类 ========== */

.transition { transition: var(--transition-all); }
.transition-none { transition: none; }
.transition-colors { transition: var(--transition-color); }
.transition-opacity { transition: var(--transition-fade); }
.transition-shadow { transition: var(--transition-box-shadow); }
.transition-transform { transition: transform var(--transition-duration-base) var(--transition-function-ease-in-out-bezier); }

/* ========== 光标工具类 ========== */

.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* ========== 用户选择工具类 ========== */

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ========== 溢出工具类 ========== */

.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-y-visible { overflow-y: visible; }
.overflow-x-scroll { overflow-x: scroll; }
.overflow-y-scroll { overflow-y: scroll; }
