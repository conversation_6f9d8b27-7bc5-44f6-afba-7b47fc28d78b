/**
 * 主题样式
 * 基于Element Plus的CSS变量系统进行定制
 */

/* Element Plus 主题变量覆盖 */
:root {
  /* 主色调 */
  --el-color-primary: var(--color-primary);
  --el-color-primary-light-1: var(--color-primary-light-1);
  --el-color-primary-light-2: var(--color-primary-light-2);
  --el-color-primary-light-3: var(--color-primary-light-3);
  --el-color-primary-light-4: var(--color-primary-light-4);
  --el-color-primary-light-5: var(--color-primary-light-5);
  --el-color-primary-light-6: var(--color-primary-light-6);
  --el-color-primary-light-7: var(--color-primary-light-7);
  --el-color-primary-light-8: var(--color-primary-light-8);
  --el-color-primary-light-9: var(--color-primary-light-9);
  --el-color-primary-dark-1: var(--color-primary-dark-1);
  --el-color-primary-dark-2: var(--color-primary-dark-2);
  
  /* 功能色 */
  --el-color-success: var(--color-success);
  --el-color-warning: var(--color-warning);
  --el-color-danger: var(--color-danger);
  --el-color-error: var(--color-danger);
  --el-color-info: var(--color-info);
  
  /* 文本颜色 */
  --el-text-color-primary: var(--color-text-primary);
  --el-text-color-regular: var(--color-text-regular);
  --el-text-color-secondary: var(--color-text-secondary);
  --el-text-color-placeholder: var(--color-text-placeholder);
  --el-text-color-disabled: var(--color-text-disabled);
  
  /* 边框颜色 */
  --el-border-color-darker: var(--color-border-darker);
  --el-border-color-dark: var(--color-border-dark);
  --el-border-color: var(--color-border);
  --el-border-color-light: var(--color-border-light);
  --el-border-color-lighter: var(--color-border-lighter);
  --el-border-color-extra-light: var(--color-border-extra-light);
  
  /* 填充色 */
  --el-fill-color-darker: var(--color-fill-darker);
  --el-fill-color-dark: var(--color-fill-dark);
  --el-fill-color: var(--color-fill);
  --el-fill-color-light: var(--color-fill-light);
  --el-fill-color-lighter: var(--color-fill-lighter);
  --el-fill-color-blank: var(--color-bg-overlay);
  
  /* 背景色 */
  --el-bg-color-page: var(--color-bg-page);
  --el-bg-color: var(--color-bg-overlay);
  --el-bg-color-overlay: var(--color-bg-overlay);
  
  /* 字体 */
  --el-font-family: var(--font-family-primary);
  --el-font-size-extra-large: var(--font-size-extra-large);
  --el-font-size-large: var(--font-size-large);
  --el-font-size-medium: var(--font-size-medium);
  --el-font-size-base: var(--font-size-base);
  --el-font-size-small: var(--font-size-small);
  --el-font-size-extra-small: var(--font-size-extra-small);
  
  /* 圆角 - 扁平化设计，使用更小的圆角 */
  --el-border-radius-base: var(--flat-border-radius);
  --el-border-radius-small: var(--border-radius-small);
  --el-border-radius-round: var(--border-radius-round);
  --el-border-radius-circle: var(--border-radius-circle);
  
  /* 阴影 - 扁平化设计，使用更轻的阴影 */
  --el-box-shadow-light: var(--flat-shadow-light);
  --el-box-shadow: var(--flat-shadow-base);
  --el-box-shadow-dark: var(--flat-shadow-hover);
  
  /* 组件尺寸 */
  --el-component-size-large: var(--component-size-large);
  --el-component-size: var(--component-size-default);
  --el-component-size-small: var(--component-size-small);
  
  /* 过渡动画 */
  --el-transition-duration: var(--transition-duration-base);
  --el-transition-duration-fast: var(--transition-duration-fast);
  --el-transition-function-ease-in-out-bezier: var(--transition-function-ease-in-out-bezier);
  --el-transition-function-fast-bezier: var(--transition-function-fast-bezier);
  --el-transition-all: var(--transition-all);
  --el-transition-fade: var(--transition-fade);
  --el-transition-md-fade: var(--transition-md-fade);
  --el-transition-border: var(--transition-border);
  --el-transition-box-shadow: var(--transition-box-shadow);
  --el-transition-color: var(--transition-color);
  
  /* Z-index */
  --el-index-normal: var(--z-index-normal);
  --el-index-top: var(--z-index-top);
  --el-index-popper: var(--z-index-popper);
  
  /* 间距 - 紧凑设计 */
  --el-space-xs: var(--compact-spacing-xs);
  --el-space-sm: var(--compact-spacing-sm);
  --el-space-md: var(--compact-spacing-md);
  --el-space-lg: var(--compact-spacing-lg);
  --el-space-xl: var(--compact-spacing-xl);
}

/* 扁平化组件样式定制 */

/* 按钮扁平化 */
.el-button {
  border-radius: var(--flat-border-radius);
  box-shadow: none;
  border-width: var(--flat-border-width);
  font-weight: var(--font-weight-normal);
  padding: var(--compact-spacing-sm) var(--compact-spacing-lg);
}

.el-button:hover,
.el-button:focus {
  box-shadow: var(--flat-shadow-hover);
}

.el-button--large {
  padding: var(--compact-spacing-md) var(--compact-spacing-xl);
}

.el-button--small {
  padding: var(--compact-spacing-xs) var(--compact-spacing-md);
}

/* 卡片扁平化 */
.el-card {
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  border: var(--flat-border);
}

.el-card:hover {
  box-shadow: var(--flat-shadow-base);
}

.el-card__header {
  padding: var(--compact-spacing-lg);
  border-bottom: var(--flat-border);
  background-color: var(--color-fill-lighter);
}

.el-card__body {
  padding: var(--compact-spacing-lg);
}

/* 表单扁平化 */
.el-input__wrapper {
  border-radius: var(--flat-border-radius);
  box-shadow: none;
  border: var(--flat-border);
}

.el-input__wrapper:hover {
  border-color: var(--color-border-dark);
}

.el-input__wrapper.is-focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light-8);
}

.el-textarea__inner {
  border-radius: var(--flat-border-radius);
  border: var(--flat-border);
  box-shadow: none;
}

.el-select .el-input__wrapper {
  border-radius: var(--flat-border-radius);
}

/* 表格扁平化 */
.el-table {
  border-radius: var(--flat-border-radius-large);
  overflow: hidden;
}

.el-table th.el-table__cell {
  background-color: var(--color-fill-light);
  border-bottom: var(--flat-border);
  padding: var(--compact-spacing-md) var(--compact-spacing-lg);
}

.el-table td.el-table__cell {
  padding: var(--compact-spacing-md) var(--compact-spacing-lg);
  border-bottom: 1px solid var(--color-border-lighter);
}

.el-table--border .el-table__cell {
  border-right: 1px solid var(--color-border-lighter);
}

/* 对话框扁平化 */
.el-dialog {
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-dark);
}

.el-dialog__header {
  padding: var(--compact-spacing-lg) var(--compact-spacing-xl);
  border-bottom: var(--flat-border);
  background-color: var(--color-fill-lighter);
}

.el-dialog__body {
  padding: var(--compact-spacing-xl);
}

.el-dialog__footer {
  padding: var(--compact-spacing-lg) var(--compact-spacing-xl);
  border-top: var(--flat-border);
  background-color: var(--color-fill-lighter);
}

/* 标签扁平化 */
.el-tag {
  border-radius: var(--flat-border-radius);
  border: var(--flat-border);
  padding: var(--compact-spacing-xs) var(--compact-spacing-sm);
  font-size: var(--font-size-small);
}

/* 分页器紧凑化 */
.el-pagination {
  padding: var(--compact-spacing-md) 0;
}

.el-pagination .el-pager li {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  border-radius: var(--flat-border-radius);
  margin: 0 2px;
}

/* 菜单扁平化 */
.el-menu {
  border-right: none;
}

.el-menu-item {
  border-radius: var(--flat-border-radius);
  margin: var(--compact-spacing-xs) var(--compact-spacing-sm);
  padding: 0 var(--compact-spacing-lg);
}

.el-menu-item:hover,
.el-menu-item.is-active {
  background-color: var(--color-primary-light-9);
  color: var(--color-primary);
}

/* 下拉菜单扁平化 */
.el-dropdown-menu {
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-base);
  border: var(--flat-border);
  padding: var(--compact-spacing-sm);
}

.el-dropdown-menu__item {
  border-radius: var(--flat-border-radius);
  padding: var(--compact-spacing-sm) var(--compact-spacing-lg);
  margin: var(--compact-spacing-xs) 0;
}

/* 开关紧凑化 */
.el-switch {
  height: 20px;
}

.el-switch__core {
  height: 20px;
  min-width: 36px;
  border-radius: 10px;
}

.el-switch__action {
  width: 16px;
  height: 16px;
}
