<template>
  <div class="dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">仪表盘</h1>
        <p class="page-subtitle">系统概览和实时监控</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in statsData" :key="stat.key">
        <div class="stat-card__header">
          <h3 class="stat-card__title">{{ stat.title }}</h3>
          <div class="stat-card__icon" :style="{ backgroundColor: stat.color + '20', color: stat.color }">
            <el-icon :size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
        </div>
        <div class="stat-card__value">{{ stat.value }}</div>
        <div class="stat-card__change" :class="stat.changeType">
          <el-icon :size="12">
            <component :is="stat.changeIcon" />
          </el-icon>
          {{ stat.change }}
        </div>
      </div>
    </div>

    <!-- 图表和活动区域 -->
    <div class="content-grid">
      <div class="chart-section">
        <div class="info-card">
          <div class="info-card__header">
            <h3 class="info-card__title">应用状态分布</h3>
            <el-dropdown>
              <el-button text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>导出数据</el-dropdown-item>
                  <el-dropdown-item>查看详情</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="info-card__body">
            <div class="chart-container">
              <div class="chart-placeholder">
                <el-icon :size="48" color="var(--color-text-placeholder)">
                  <PieChart />
                </el-icon>
                <p>应用状态图表</p>
                <el-button type="primary" text>配置图表</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="activity-section">
        <div class="info-card">
          <div class="info-card__header">
            <h3 class="info-card__title">最近活动</h3>
            <el-button type="primary" text @click="viewAllActivities">
              查看全部
            </el-button>
          </div>
          <div class="info-card__body">
            <div class="activity-list">
              <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                <div class="activity-avatar">
                  <el-avatar :size="32" :src="activity.avatar">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                </div>
                <div class="activity-content">
                  <div class="activity-text">{{ activity.content }}</div>
                  <div class="activity-meta">
                    <span class="activity-time">{{ activity.time }}</span>
                    <el-tag :type="activity.type" size="small">{{ activity.category }}</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Monitor,
  CircleCheck,
  Timer,
  User,
  Refresh,
  MoreFilled,
  PieChart,
  TrendCharts,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

// 统计数据
const stats = ref({
  totalApps: 0,
  runningApps: 0,
  totalTasks: 0,
  totalUsers: 0
})

// 统计卡片数据
const statsData = computed(() => [
  {
    key: 'totalApps',
    title: '应用总数',
    value: stats.value.totalApps,
    change: '+2 本月',
    changeType: 'stat-card__change--positive',
    changeIcon: ArrowUp,
    color: 'var(--color-primary)',
    icon: Monitor
  },
  {
    key: 'runningApps',
    title: '运行中应用',
    value: stats.value.runningApps,
    change: '+1 今日',
    changeType: 'stat-card__change--positive',
    changeIcon: ArrowUp,
    color: 'var(--color-success)',
    icon: CircleCheck
  },
  {
    key: 'totalTasks',
    title: '任务总数',
    value: stats.value.totalTasks,
    change: '+5 本周',
    changeType: 'stat-card__change--positive',
    changeIcon: ArrowUp,
    color: 'var(--color-warning)',
    icon: Timer
  },
  {
    key: 'totalUsers',
    title: '用户总数',
    value: stats.value.totalUsers,
    change: '无变化',
    changeType: 'stat-card__change--neutral',
    changeIcon: TrendCharts,
    color: 'var(--color-info)',
    icon: User
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    time: '2分钟前',
    content: '用户 admin 启动了应用 web-app',
    category: '应用',
    type: 'success',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'
  },
  {
    id: 2,
    time: '5分钟前',
    content: '任务 backup-task 执行完成',
    category: '任务',
    type: 'info',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=system'
  },
  {
    id: 3,
    time: '10分钟前',
    content: '用户 developer 创建了新应用 api-service',
    category: '应用',
    type: 'success',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=developer'
  },
  {
    id: 4,
    time: '15分钟前',
    content: '系统监控发现异常，已自动处理',
    category: '监控',
    type: 'warning',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=monitor'
  }
])

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用API获取真实数据
    // const response = await dashboardApi.getStats()
    // stats.value = response.data

    // 模拟数据
    stats.value = {
      totalApps: 12,
      runningApps: 8,
      totalTasks: 25,
      totalUsers: 15
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await loadStats()
}

// 查看所有活动
const viewAllActivities = () => {
  // 跳转到活动日志页面
  console.log('查看所有活动')
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--compact-spacing-lg);
  margin-bottom: var(--compact-spacing-xl);
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--compact-spacing-lg);
}

.chart-section {
  min-height: 400px;
}

.activity-section {
  min-height: 400px;
}

/* 图表容器 */
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--compact-spacing-md);
  color: var(--color-text-placeholder);
  text-align: center;
}

.chart-placeholder p {
  margin: 0;
  font-size: var(--font-size-base);
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--compact-spacing-md);
  max-height: 350px;
  overflow-y: auto;
  padding-right: var(--compact-spacing-xs);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--compact-spacing-md);
  padding: var(--compact-spacing-md);
  border-radius: var(--flat-border-radius);
  transition: var(--transition-all);
  border: var(--flat-border);
  border-color: transparent;
}

.activity-item:hover {
  background-color: var(--color-fill-light);
  border-color: var(--color-border-light);
}

.activity-avatar {
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  margin-bottom: var(--compact-spacing-xs);
  line-height: var(--line-height-base);
}

.activity-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--compact-spacing-md);
}

.activity-time {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--compact-spacing-lg);
  }

  .activity-section {
    order: -1;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--compact-spacing-md);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--compact-spacing-md);
  }

  .page-actions {
    width: 100%;
  }

  .activity-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--compact-spacing-xs);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    gap: var(--compact-spacing-sm);
  }

  .content-grid {
    gap: var(--compact-spacing-md);
  }

  .activity-item {
    padding: var(--compact-spacing-sm);
  }

  .activity-text {
    font-size: var(--font-size-small);
  }
}
</style>