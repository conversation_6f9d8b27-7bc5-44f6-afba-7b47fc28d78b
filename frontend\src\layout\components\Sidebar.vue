<template>
  <div class="sidebar">
    <!-- Logo区域 -->
    <div class="sidebar-logo" :class="{ 'collapsed': collapsed }">
      <div class="logo-icon">
        <el-icon size="24"><Monitor /></el-icon>
      </div>
      <div v-if="!collapsed" class="logo-text">
        <span class="logo-title">APM</span>
        <span class="logo-subtitle">管理系统</span>
      </div>
    </div>

    <!-- 菜单区域 -->
    <el-scrollbar class="sidebar-menu-container">
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
        class="sidebar-menu"
        @select="handleSelect"
      >
        <el-sub-menu
          v-for="route in menuRoutes.filter(r => r.children && r.children.length > 0)"
          :key="route.path"
          :index="route.path"
          class="menu-submenu"
        >
          <template #title>
            <div class="menu-item-content">
              <el-icon v-if="route.meta?.icon" class="menu-icon">
                <component :is="route.meta.icon" />
              </el-icon>
              <span class="menu-title">{{ route.meta?.title }}</span>
            </div>
          </template>
          <el-menu-item
            v-for="child in route.children"
            :key="child.path"
            :index="child.path"
            class="menu-item-child"
          >
            <div class="menu-item-content">
              <el-icon v-if="child.meta?.icon" class="menu-icon">
                <component :is="child.meta.icon" />
              </el-icon>
              <span class="menu-title">{{ child.meta?.title }}</span>
            </div>
          </el-menu-item>
        </el-sub-menu>

        <el-menu-item
          v-for="route in menuRoutes.filter(r => !r.children || r.children.length === 0)"
          :key="route.path"
          :index="route.path"
          class="menu-item"
        >
          <div class="menu-item-content">
            <el-icon v-if="route.meta?.icon" class="menu-icon">
              <component :is="route.meta.icon" />
            </el-icon>
            <span class="menu-title">{{ route.meta?.title }}</span>
          </div>
        </el-menu-item>
      </el-menu>
    </el-scrollbar>

    <!-- 底部信息 -->
    <div v-if="!collapsed" class="sidebar-footer">
      <div class="version-info">
        <span class="version-text">v1.0.0</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Monitor } from '@element-plus/icons-vue'

interface Props {
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const route = useRoute()
const router = useRouter()

const activeMenu = computed(() => route.path)

// 获取菜单路由
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  const layoutRoute = routes.find(r => r.name === 'Layout')
  return layoutRoute?.children?.filter(child => child.meta?.title) || []
})

const handleSelect = (index: string) => {
  router.push(index)
}
</script>

<style scoped>
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-bg-overlay);
  border-right: var(--flat-border);
}

/* Logo区域 */
.sidebar-logo {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
  padding: var(--compact-spacing-lg);
  border-bottom: var(--flat-border);
  background: var(--color-fill-lighter);
  transition: var(--transition-all);
  min-height: var(--header-height);
}

.sidebar-logo.collapsed {
  justify-content: center;
  padding: var(--compact-spacing-lg) var(--compact-spacing-md);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--color-primary);
  color: white;
  border-radius: var(--flat-border-radius);
  flex-shrink: 0;
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: var(--compact-spacing-xs);
  min-width: 0;
}

.logo-title {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: 1;
}

.logo-subtitle {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  line-height: 1;
}

/* 菜单容器 */
.sidebar-menu-container {
  flex: 1;
  overflow: hidden;
}

/* 菜单样式 */
.sidebar-menu {
  border-right: none;
  background: transparent;
  padding: var(--compact-spacing-md);
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  height: auto;
  line-height: 1;
  padding: 0;
  margin-bottom: var(--compact-spacing-xs);
  border-radius: var(--flat-border-radius);
  transition: var(--transition-all);
  background: transparent;
  border: var(--flat-border);
  border-color: transparent;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background: var(--color-fill-light);
  border-color: var(--color-border-light);
  color: var(--color-primary);
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: var(--color-primary-light-9);
  border-color: var(--color-primary-light-6);
  color: var(--color-primary);
}

.sidebar-menu :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
  background: var(--color-primary-light-9);
  border-color: var(--color-primary-light-6);
  color: var(--color-primary);
}

/* 菜单项内容 */
.menu-item-content {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
  padding: var(--compact-spacing-md);
  width: 100%;
}

.menu-icon {
  font-size: 16px;
  flex-shrink: 0;
  transition: var(--transition-color);
}

.menu-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: var(--transition-color);
}

/* 子菜单样式 */
.sidebar-menu :deep(.el-sub-menu) {
  margin-bottom: var(--compact-spacing-xs);
}

.sidebar-menu :deep(.el-sub-menu .el-menu) {
  background: transparent;
  padding: var(--compact-spacing-xs) 0 0 0;
}

.menu-item-child {
  margin-left: var(--compact-spacing-lg);
  margin-bottom: var(--compact-spacing-xs);
}

.menu-item-child .menu-item-content {
  padding: var(--compact-spacing-sm) var(--compact-spacing-md);
}

.menu-item-child .menu-icon {
  font-size: 14px;
}

.menu-item-child .menu-title {
  font-size: var(--font-size-small);
}

/* 折叠状态 */
.sidebar-menu.el-menu--collapse {
  padding: var(--compact-spacing-md) var(--compact-spacing-sm);
}

.sidebar-menu.el-menu--collapse .menu-item-content {
  justify-content: center;
  padding: var(--compact-spacing-md) var(--compact-spacing-sm);
}

/* 底部信息 */
.sidebar-footer {
  padding: var(--compact-spacing-lg);
  border-top: var(--flat-border);
  background: var(--color-fill-lighter);
}

.version-info {
  text-align: center;
}

.version-text {
  font-size: var(--font-size-small);
  color: var(--color-text-placeholder);
  font-weight: var(--font-weight-normal);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-logo {
    padding: var(--compact-spacing-md);
  }

  .sidebar-menu {
    padding: var(--compact-spacing-sm);
  }

  .menu-item-content {
    padding: var(--compact-spacing-sm);
  }
}
</style>