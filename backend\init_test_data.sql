-- ========================================
-- 应用项目管理系统 - 初始化测试数据
-- ========================================
-- 说明：
-- 1. 默认超级管理员账号: admin / admin123
-- 2. 默认普通用户账号: user / user123
-- 3. 包含完整的权限、角色、菜单、部门结构
-- ========================================

-- 清理现有数据（按依赖关系逆序删除）
DELETE FROM user_departments;
DELETE FROM user_roles;
DELETE FROM role_permissions;
DELETE FROM role_menus;
DELETE FROM task_executions;
DELETE FROM task_dependencies;
DELETE FROM tasks;
DELETE FROM app_services;
DELETE FROM app_logs;
DELETE FROM app_configs;
DELETE FROM log_alert_records;
DELETE FROM log_alerts;
DELETE FROM apps;
DELETE FROM alert_rules;
DELETE FROM system_alerts;
DELETE FROM system_metrics;
DELETE FROM system_processes;
DELETE FROM menus;
DELETE FROM users;
DELETE FROM roles;
DELETE FROM permissions;
DELETE FROM departments;

-- 重置序列
ALTER SEQUENCE departments_id_seq RESTART WITH 1;
ALTER SEQUENCE permissions_id_seq RESTART WITH 1;
ALTER SEQUENCE roles_id_seq RESTART WITH 1;
ALTER SEQUENCE users_id_seq RESTART WITH 1;
ALTER SEQUENCE menus_id_seq RESTART WITH 1;
ALTER SEQUENCE apps_id_seq RESTART WITH 1;
ALTER SEQUENCE tasks_id_seq RESTART WITH 1;
ALTER SEQUENCE alert_rules_id_seq RESTART WITH 1;

-- ========================================
-- 1. 部门数据
-- ========================================
INSERT INTO departments (id, name, description, parent_id, sort_order, created_at, updated_at) VALUES
(1, '总公司', '公司总部', NULL, 1, NOW(), NOW()),
(2, '技术部', '负责技术开发和维护', 1, 1, NOW(), NOW()),
(3, '运维部', '负责系统运维和监控', 1, 2, NOW(), NOW()),
(4, '产品部', '负责产品设计和管理', 1, 3, NOW(), NOW()),
(5, '前端组', '前端开发团队', 2, 1, NOW(), NOW()),
(6, '后端组', '后端开发团队', 2, 2, NOW(), NOW()),
(7, '测试组', '质量保证团队', 2, 3, NOW(), NOW()),
(8, '基础设施组', '基础设施维护', 3, 1, NOW(), NOW()),
(9, '监控组', '系统监控和告警', 3, 2, NOW(), NOW());

-- ========================================
-- 2. 权限数据
-- ========================================
INSERT INTO permissions (id, code, name, description, created_at) VALUES
-- 用户管理权限
(1, 'user:read', '查看用户', '查看用户列表和详情', NOW()),
(2, 'user:create', '创建用户', '创建新用户账号', NOW()),
(3, 'user:update', '编辑用户', '编辑用户信息', NOW()),
(4, 'user:delete', '删除用户', '删除用户账号', NOW()),
(5, 'user:manage_roles', '管理角色', '分配和移除用户角色', NOW()),

-- 角色管理权限
(6, 'role:read', '查看角色', '查看角色列表和详情', NOW()),
(7, 'role:create', '创建角色', '创建新角色', NOW()),
(8, 'role:update', '编辑角色', '编辑角色信息', NOW()),
(9, 'role:delete', '删除角色', '删除角色', NOW()),
(10, 'permission:read', '查看权限', '查看权限列表和详情', NOW()),
(11, 'permission:create', '创建权限', '创建新权限', NOW()),

-- 部门管理权限
(12, 'department:read', '查看部门', '查看部门结构', NOW()),
(13, 'department:create', '创建部门', '创建新部门', NOW()),
(14, 'department:update', '编辑部门', '编辑部门信息', NOW()),
(15, 'department:delete', '删除部门', '删除部门', NOW()),

-- 应用管理权限
(16, 'app:read', '查看应用', '查看应用列表和详情', NOW()),
(17, 'app:create', '创建应用', '创建新应用', NOW()),
(18, 'app:update', '编辑应用', '编辑应用配置', NOW()),
(19, 'app:delete', '删除应用', '删除应用', NOW()),
(20, 'app:control', '控制应用', '启动、停止、重启应用服务', NOW()),
(21, 'app:logs', '查看日志', '查看应用运行日志', NOW()),

-- 任务管理权限
(22, 'task:read', '查看任务', '查看任务列表和详情', NOW()),
(23, 'task:create', '创建任务', '创建新任务', NOW()),
(24, 'task:update', '编辑任务', '编辑任务配置', NOW()),
(25, 'task:delete', '删除任务', '删除任务', NOW()),
(26, 'task:execute', '执行任务', '手动执行任务', NOW()),

-- 系统监控权限
(27, 'monitor:read', '查看监控', '查看系统监控数据', NOW()),
(28, 'alert:read', '查看告警', '查看告警规则和记录', NOW()),
(29, 'alert:create', '创建告警', '创建告警规则', NOW()),
(30, 'alert:update', '编辑告警', '编辑告警规则', NOW()),
(31, 'alert:delete', '删除告警', '删除告警规则', NOW()),

-- 菜单管理权限
(32, 'menu:read', '查看菜单', '查看菜单结构', NOW()),
(33, 'menu:create', '创建菜单', '创建新菜单', NOW()),
(34, 'menu:update', '编辑菜单', '编辑菜单信息', NOW()),
(35, 'menu:delete', '删除菜单', '删除菜单', NOW()),

-- 日志管理权限
(36, 'log:read', '查看日志', '查看应用日志', NOW()),

-- 系统管理权限
(37, 'system:admin', '系统管理', '系统管理员权限', NOW());

-- ========================================
-- 3. 角色数据
-- ========================================
INSERT INTO roles (id, name, description, created_at, updated_at) VALUES
(1, '超级管理员', '拥有系统所有权限的超级管理员', NOW(), NOW()),
(2, '系统管理员', '系统管理员，负责用户和权限管理', NOW(), NOW()),
(3, '运维管理员', '运维管理员，负责应用和系统监控', NOW(), NOW()),
(4, '开发人员', '开发人员，可以管理应用和任务', NOW(), NOW()),
(5, '普通用户', '普通用户，只能查看基本信息', NOW(), NOW());

-- ========================================
-- 4. 用户数据
-- ========================================
-- 密码说明：
-- admin123 -> $2b$12$CXSpxnIgKBo9arHcdWgtdOtD/K.W4RdCl3JLcdzThkyakj808nzJa
-- user123 -> $2b$12$Oz1B8wxhHe0OBsOTNZ69meVLdEQa.0i60uDkd2nI8gZOENDIn5Iim
INSERT INTO users (id, username, email, full_name, hashed_password, is_active, is_superuser, created_at, updated_at) VALUES
(1, 'admin', '<EMAIL>', '系统管理员', '$2b$12$CXSpxnIgKBo9arHcdWgtdOtD/K.W4RdCl3JLcdzThkyakj808nzJa', true, true, NOW(), NOW()),
(2, 'user', '<EMAIL>', '普通用户', '$2b$12$Oz1B8wxhHe0OBsOTNZ69meVLdEQa.0i60uDkd2nI8gZOENDIn5Iim', true, false, NOW(), NOW()),
(3, 'devops', '<EMAIL>', '运维工程师', '$2b$12$Oz1B8wxhHe0OBsOTNZ69meVLdEQa.0i60uDkd2nI8gZOENDIn5Iim', true, false, NOW(), NOW()),
(4, 'developer', '<EMAIL>', '开发工程师', '$2b$12$Oz1B8wxhHe0OBsOTNZ69meVLdEQa.0i60uDkd2nI8gZOENDIn5Iim', true, false, NOW(), NOW()),
(5, 'tester', '<EMAIL>', '测试工程师', '$2b$12$Oz1B8wxhHe0OBsOTNZ69meVLdEQa.0i60uDkd2nI8gZOENDIn5Iim', true, false, NOW(), NOW());

-- ========================================
-- 5. 菜单数据
-- ========================================
INSERT INTO menus (id, name, path, component, icon, parent_id, sort_order, is_hidden, is_external, created_at, updated_at) VALUES
-- 一级菜单
(1, '仪表盘', '/dashboard', 'Dashboard', 'dashboard', NULL, 1, false, false, NOW(), NOW()),
(2, '应用管理', '/apps', 'AppManagement', 'apps', NULL, 2, false, false, NOW(), NOW()),
(3, '任务管理', '/tasks', 'TaskManagement', 'task', NULL, 3, false, false, NOW(), NOW()),
(4, '系统监控', '/monitor', 'SystemMonitor', 'monitor', NULL, 4, false, false, NOW(), NOW()),
(5, '系统管理', '/system', 'SystemManagement', 'setting', NULL, 5, false, false, NOW(), NOW()),

-- 应用管理子菜单
(6, '应用列表', '/apps/list', 'AppList', 'list', 2, 1, false, false, NOW(), NOW()),
(7, '应用配置', '/apps/config', 'AppConfig', 'setting', 2, 2, false, false, NOW(), NOW()),
(8, '应用日志', '/apps/logs', 'AppLogs', 'file-text', 2, 3, false, false, NOW(), NOW()),

-- 任务管理子菜单
(9, '任务列表', '/tasks/list', 'TaskList', 'list', 3, 1, false, false, NOW(), NOW()),
(10, '任务执行', '/tasks/executions', 'TaskExecutions', 'play-circle', 3, 2, false, false, NOW(), NOW()),
(11, '任务调度', '/tasks/schedule', 'TaskSchedule', 'clock-circle', 3, 3, false, false, NOW(), NOW()),

-- 系统监控子菜单
(12, '系统状态', '/monitor/system', 'SystemStatus', 'desktop', 4, 1, false, false, NOW(), NOW()),
(13, '告警管理', '/monitor/alerts', 'AlertManagement', 'bell', 4, 2, false, false, NOW(), NOW()),
(14, '性能监控', '/monitor/performance', 'PerformanceMonitor', 'line-chart', 4, 3, false, false, NOW(), NOW()),

-- 系统管理子菜单
(15, '用户管理', '/system/users', 'UserManagement', 'user', 5, 1, false, false, NOW(), NOW()),
(16, '角色管理', '/system/roles', 'RoleManagement', 'team', 5, 2, false, false, NOW(), NOW()),
(17, '部门管理', '/system/departments', 'DepartmentManagement', 'apartment', 5, 3, false, false, NOW(), NOW()),
(18, '菜单管理', '/system/menus', 'MenuManagement', 'menu', 5, 4, false, false, NOW(), NOW());

-- ========================================
-- 6. 角色权限关联
-- ========================================
-- 超级管理员拥有所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- 系统管理员权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(2, 1), (2, 2), (2, 3), (2, 4), (2, 5),  -- 用户管理
(2, 6), (2, 7), (2, 8), (2, 9), (2, 10), -- 角色管理
(2, 11), (2, 12), (2, 13), (2, 14),      -- 部门管理
(2, 31), (2, 32), (2, 33), (2, 34);     -- 菜单管理

-- 运维管理员权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(3, 15), (3, 16), (3, 17), (3, 18), (3, 19), (3, 20), (3, 21), (3, 22), -- 应用管理
(3, 23), (3, 24), (3, 25), (3, 26), (3, 27), (3, 28), -- 任务管理
(3, 29), (3, 30); -- 系统监控

-- 开发人员权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(4, 15), (4, 16), (4, 17), (4, 22), -- 应用管理（不包括删除）
(4, 23), (4, 24), (4, 25), (4, 27), (4, 28), -- 任务管理（不包括删除）
(4, 29); -- 查看监控

-- 普通用户权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(5, 15), (5, 22), -- 查看应用和日志
(5, 23), (5, 28), -- 查看任务和执行记录
(5, 29); -- 查看监控

-- ========================================
-- 7. 用户角色关联
-- ========================================
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1), -- admin -> 超级管理员
(2, 5), -- user -> 普通用户
(3, 3), -- devops -> 运维管理员
(4, 4), -- developer -> 开发人员
(5, 5); -- tester -> 普通用户

-- ========================================
-- 8. 用户部门关联
-- ========================================
INSERT INTO user_departments (user_id, department_id) VALUES
(1, 1), -- admin -> 总公司
(2, 2), -- user -> 技术部
(3, 3), -- devops -> 运维部
(4, 6), -- developer -> 后端组
(5, 7); -- tester -> 测试组

-- ========================================
-- 9. 角色菜单关联
-- ========================================
-- 超级管理员可以访问所有菜单
INSERT INTO role_menus (role_id, menu_id)
SELECT 1, id FROM menus;

-- 系统管理员菜单权限
INSERT INTO role_menus (role_id, menu_id) VALUES
(2, 1), -- 仪表盘
(2, 5), (2, 15), (2, 16), (2, 17), (2, 18); -- 系统管理及其子菜单

-- 运维管理员菜单权限
INSERT INTO role_menus (role_id, menu_id) VALUES
(3, 1), -- 仪表盘
(3, 2), (3, 6), (3, 7), (3, 8), -- 应用管理及其子菜单
(3, 3), (3, 9), (3, 10), (3, 11), -- 任务管理及其子菜单
(3, 4), (3, 12), (3, 13), (3, 14); -- 系统监控及其子菜单

-- 开发人员菜单权限
INSERT INTO role_menus (role_id, menu_id) VALUES
(4, 1), -- 仪表盘
(4, 2), (4, 6), (4, 7), (4, 8), -- 应用管理及其子菜单
(4, 3), (4, 9), (4, 10), (4, 11), -- 任务管理及其子菜单
(4, 4), (4, 12), (4, 14); -- 系统监控（部分）

-- 普通用户菜单权限
INSERT INTO role_menus (role_id, menu_id) VALUES
(5, 1), -- 仪表盘
(5, 2), (5, 6), (5, 8), -- 应用管理（查看）
(5, 3), (5, 9), (5, 10), -- 任务管理（查看）
(5, 4), (5, 12), (5, 14); -- 系统监控（查看）

-- ========================================
-- 10. 示例应用数据
-- ========================================
INSERT INTO apps (id, name, description, frontend_dir, backend_dir, frontend_start_cmd, backend_start_cmd,
                  frontend_stop_cmd, backend_stop_cmd, frontend_port, backend_port, is_active, created_by,
                  created_at, updated_at) VALUES
(1, '用户管理系统', '企业用户管理系统，提供用户注册、登录、权限管理等功能',
    '/opt/apps/user-system/frontend', '/opt/apps/user-system/backend',
    'npm start', 'python main.py',
    'npm stop', 'pkill -f main.py',
    3000, 8000, true, 1, NOW(), NOW()),

(2, '订单管理系统', '电商订单管理系统，处理订单创建、支付、发货等流程',
    '/opt/apps/order-system/frontend', '/opt/apps/order-system/backend',
    'npm run dev', 'uvicorn main:app --host 0.0.0.0 --port 8001',
    'npm stop', 'pkill -f uvicorn',
    3001, 8001, true, 4, NOW(), NOW()),

(3, '监控仪表盘', '系统监控仪表盘，实时显示系统性能指标和告警信息',
    '/opt/apps/monitor-dashboard/frontend', '/opt/apps/monitor-dashboard/backend',
    'yarn start', 'python app.py',
    'yarn stop', 'pkill -f app.py',
    3002, 8002, false, 3, NOW(), NOW());

-- ========================================
-- 11. 示例任务数据
-- ========================================
INSERT INTO tasks (id, name, description, task_type, app_id, command, parameters, working_directory,
                   environment_vars, schedule_type, cron_expression, interval_seconds, is_active,
                   max_retries, timeout_seconds, created_by, created_at, updated_at) VALUES
-- 数据库备份任务
(1, '数据库备份', '每日凌晨2点执行数据库备份', 'command', NULL,
    'pg_dump -h localhost -U postgres -d app_manager > /backup/db_backup_$(date +%Y%m%d).sql',
    '{"backup_path": "/backup", "retention_days": 7}',
    '/backup',
    '{"PGPASSWORD": "password"}',
    'cron', '0 2 * * *', NULL, true, 3, 3600, 1, NOW(), NOW()),

-- 日志清理任务
(2, '日志清理', '清理7天前的应用日志文件', 'command', 1,
    'find /opt/apps/*/logs -name "*.log" -mtime +7 -delete',
    '{"retention_days": 7}',
    '/opt/apps',
    NULL,
    'cron', '0 3 * * 0', NULL, true, 2, 1800, 3, NOW(), NOW()),

-- 系统健康检查
(3, '系统健康检查', '每5分钟检查系统关键服务状态', 'script', NULL,
    'python /scripts/health_check.py',
    '{"services": ["postgresql", "redis", "nginx"], "alert_threshold": 3}',
    '/scripts',
    NULL,
    'interval', NULL, 300, true, 1, 300, 3, NOW(), NOW()),

-- 应用部署任务
(4, '用户系统部署', '部署用户管理系统到生产环境', 'command', 1,
    'bash /scripts/deploy.sh user-system production',
    '{"environment": "production", "branch": "main"}',
    '/scripts',
    '{"DEPLOY_KEY": "/keys/deploy_key"}',
    'manual', NULL, NULL, true, 0, 1800, 4, NOW(), NOW()),

-- 数据同步任务
(5, '数据同步', '同步用户数据到数据仓库', 'database', 1,
    'SELECT sync_user_data_to_warehouse();',
    '{"target_db": "warehouse", "batch_size": 1000}',
    NULL,
    '{"WAREHOUSE_DB_URL": "*************************************/dw"}',
    'cron', '0 1 * * *', NULL, true, 2, 7200, 1, NOW(), NOW());

-- ========================================
-- 12. 任务依赖关系
-- ========================================
INSERT INTO task_dependencies (task_id, depends_on_task_id, dependency_type, is_active, created_at) VALUES
(5, 1, 'success', true, NOW()), -- 数据同步依赖数据库备份成功
(2, 3, 'always', true, NOW());  -- 日志清理总是在健康检查后执行

-- ========================================
-- 13. 系统告警规则
-- ========================================
INSERT INTO alert_rules (id, name, description, metric_name, threshold_value, comparison_operator,
                         severity, duration_minutes, evaluation_interval, notification_enabled,
                         notification_channels, is_active, created_by, created_at, updated_at) VALUES
(1, 'CPU使用率过高', '当CPU使用率超过80%持续5分钟时触发告警', 'cpu_percent', 80.0, '>',
    'high', 5, 60, true, '["email", "webhook"]', true, 'admin', NOW(), NOW()),

(2, '内存使用率过高', '当内存使用率超过85%持续3分钟时触发告警', 'memory_percent', 85.0, '>',
    'high', 3, 60, true, '["email"]', true, 'admin', NOW(), NOW()),

(3, '磁盘空间不足', '当磁盘使用率超过90%时立即触发告警', 'disk_percent', 90.0, '>',
    'critical', 1, 60, true, '["email", "sms", "webhook"]', true, 'admin', NOW(), NOW()),

(4, '应用响应时间过长', '当应用响应时间超过2秒持续2分钟时触发告警', 'response_time', 2000.0, '>',
    'medium', 2, 30, true, '["email"]', true, 'admin', NOW(), NOW()),

(5, '数据库连接数过多', '当数据库连接数超过100时触发告警', 'db_connections', 100.0, '>',
    'medium', 1, 120, true, '["email"]', true, 'admin', NOW(), NOW());

-- ========================================
-- 14. 日志告警规则
-- ========================================
INSERT INTO log_alerts (id, app_id, rule_name, level, pattern, threshold, time_window, is_active,
                        created_at, updated_at) VALUES
(1, 1, '用户系统错误告警', 'ERROR', 'ERROR|Exception|Failed', 5, 5, true, NOW(), NOW()),
(2, 2, '订单系统支付失败', 'ERROR', 'payment.*failed|支付失败', 3, 10, true, NOW(), NOW()),
(3, 1, '用户系统登录异常', 'WARN', 'login.*failed|登录失败', 10, 5, true, NOW(), NOW()),
(4, 3, '监控系统连接异常', 'ERROR', 'connection.*timeout|连接超时', 2, 3, true, NOW(), NOW());

-- ========================================
-- 数据插入完成提示
-- ========================================
-- 查看插入的数据统计
SELECT
    '部门' as table_name, COUNT(*) as count FROM departments
UNION ALL SELECT '权限', COUNT(*) FROM permissions
UNION ALL SELECT '角色', COUNT(*) FROM roles
UNION ALL SELECT '用户', COUNT(*) FROM users
UNION ALL SELECT '菜单', COUNT(*) FROM menus
UNION ALL SELECT '应用', COUNT(*) FROM apps
UNION ALL SELECT '任务', COUNT(*) FROM tasks
UNION ALL SELECT '告警规则', COUNT(*) FROM alert_rules
UNION ALL SELECT '日志告警', COUNT(*) FROM log_alerts
ORDER BY table_name;

-- 显示默认账号信息
SELECT
    '=== 默认账号信息 ===' as info
UNION ALL SELECT '超级管理员: admin / admin123'
UNION ALL SELECT '普通用户: user / user123'
UNION ALL SELECT '运维工程师: devops / user123'
UNION ALL SELECT '开发工程师: developer / user123'
UNION ALL SELECT '测试工程师: tester / user123'
UNION ALL SELECT '=== 初始化完成 ===';

COMMIT;
