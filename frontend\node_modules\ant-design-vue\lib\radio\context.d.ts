import type { RadioGroupContext, RadioOptionTypeContextProps } from './interface';
export declare const useProvideRadioGroupContext: (props: RadioGroupContext) => void;
export declare const useInjectRadioGroupContext: () => RadioGroupContext;
export declare const useProvideRadioOptionTypeContext: (props: RadioOptionTypeContextProps) => void;
export declare const useInjectRadioOptionTypeContext: () => import("./interface").RadioGroupOptionType;
