/**
 * 设计令牌 (Design Tokens)
 * 定义系统的基础设计变量
 */

:root {
  /* ========== 颜色系统 ========== */
  
  /* 主色调 */
  --color-primary: #409eff;
  --color-primary-light-1: #53a8ff;
  --color-primary-light-2: #66b1ff;
  --color-primary-light-3: #79bbff;
  --color-primary-light-4: #8cc5ff;
  --color-primary-light-5: #a0cfff;
  --color-primary-light-6: #b3d8ff;
  --color-primary-light-7: #c6e2ff;
  --color-primary-light-8: #d9ecff;
  --color-primary-light-9: #ecf5ff;
  --color-primary-dark-1: #337ecc;
  --color-primary-dark-2: #2d70b3;
  
  /* 功能色 */
  --color-success: #67c23a;
  --color-success-light: #95d475;
  --color-success-lighter: #b3e19d;
  --color-success-extra-light: #f0f9ff;
  
  --color-warning: #e6a23c;
  --color-warning-light: #ebb563;
  --color-warning-lighter: #f5dab1;
  --color-warning-extra-light: #fdf6ec;
  
  --color-danger: #f56c6c;
  --color-danger-light: #f78989;
  --color-danger-lighter: #fab6b6;
  --color-danger-extra-light: #fef0f0;
  
  --color-info: #909399;
  --color-info-light: #a6a9ad;
  --color-info-lighter: #c8c9cc;
  --color-info-extra-light: #f4f4f5;
  
  /* 中性色 */
  --color-text-primary: #303133;
  --color-text-regular: #606266;
  --color-text-secondary: #909399;
  --color-text-placeholder: #a8abb2;
  --color-text-disabled: #c0c4cc;
  
  --color-border-darker: #cdd0d6;
  --color-border-dark: #d4d7de;
  --color-border: #dcdfe6;
  --color-border-light: #e4e7ed;
  --color-border-lighter: #ebeef5;
  --color-border-extra-light: #f2f6fc;
  
  --color-fill-darker: #e6e8eb;
  --color-fill-dark: #ebedf0;
  --color-fill: #f0f2f5;
  --color-fill-light: #f5f7fa;
  --color-fill-lighter: #fafafa;
  
  --color-bg-page: #f2f3f5;
  --color-bg-overlay: #ffffff;
  
  /* ========== 字体系统 ========== */
  
  /* 字体族 */
  --font-family-primary: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  
  /* 字体大小 */
  --font-size-extra-large: 20px;
  --font-size-large: 18px;
  --font-size-medium: 16px;
  --font-size-base: 14px;
  --font-size-small: 13px;
  --font-size-extra-small: 12px;
  
  /* 行高 */
  --line-height-base: 1.5;
  --line-height-small: 1.2;
  --line-height-large: 1.8;
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  --font-weight-bolder: 700;
  
  /* ========== 间距系统 ========== */
  
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;
  --spacing-5xl: 48px;
  
  /* ========== 圆角系统 ========== */
  
  --border-radius-none: 0;
  --border-radius-small: 2px;
  --border-radius-base: 4px;
  --border-radius-large: 6px;
  --border-radius-round: 20px;
  --border-radius-circle: 50%;
  
  /* ========== 阴影系统 ========== */
  
  --box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.12);
  
  /* ========== 过渡动画 ========== */
  
  --transition-duration-fast: 0.2s;
  --transition-duration-base: 0.3s;
  --transition-duration-slow: 0.5s;
  
  --transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
  
  --transition-all: all var(--transition-duration-base) var(--transition-function-ease-in-out-bezier);
  --transition-fade: opacity var(--transition-duration-base) var(--transition-function-fast-bezier);
  --transition-md-fade: transform var(--transition-duration-base) var(--transition-function-fast-bezier), opacity var(--transition-duration-base) var(--transition-function-fast-bezier);
  --transition-border: border-color var(--transition-duration-base) var(--transition-function-ease-in-out-bezier);
  --transition-box-shadow: box-shadow var(--transition-duration-base) var(--transition-function-ease-in-out-bezier);
  --transition-color: color var(--transition-duration-base) var(--transition-function-ease-in-out-bezier);
  
  /* ========== Z-index 层级 ========== */
  
  --z-index-normal: 1;
  --z-index-top: 1000;
  --z-index-popper: 2000;
  --z-index-dialog: 3000;
  --z-index-message: 4000;
  --z-index-loading: 5000;
  
  /* ========== 断点系统 ========== */
  
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1600px;
  
  /* ========== 组件尺寸 ========== */
  
  --component-size-large: 40px;
  --component-size-default: 32px;
  --component-size-small: 24px;
  
  /* ========== 布局尺寸 ========== */
  
  --header-height: 60px;
  --sidebar-width: 200px;
  --sidebar-width-collapsed: 64px;
  --footer-height: 60px;
  
  /* ========== 扁平化设计特定变量 ========== */
  
  /* 减少阴影，更扁平 */
  --flat-shadow-light: 0 1px 2px rgba(0, 0, 0, 0.08);
  --flat-shadow-base: 0 1px 3px rgba(0, 0, 0, 0.12);
  --flat-shadow-hover: 0 2px 4px rgba(0, 0, 0, 0.16);
  
  /* 更小的圆角 */
  --flat-border-radius: 2px;
  --flat-border-radius-large: 4px;
  
  /* 更紧凑的间距 */
  --compact-spacing-xs: 2px;
  --compact-spacing-sm: 4px;
  --compact-spacing-md: 8px;
  --compact-spacing-lg: 12px;
  --compact-spacing-xl: 16px;
  
  /* 更细的边框 */
  --flat-border-width: 1px;
  --flat-border-style: solid;
  --flat-border: var(--flat-border-width) var(--flat-border-style) var(--color-border);
}

/* 暗色主题变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg-page: #141414;
    --color-bg-overlay: #1f1f1f;
    --color-text-primary: #e5eaf3;
    --color-text-regular: #cfd3dc;
    --color-text-secondary: #a3a6ad;
    --color-border: #4c4d4f;
    --color-border-light: #414243;
    --color-fill: #262727;
    --color-fill-light: #1d1e1f;
  }
}
