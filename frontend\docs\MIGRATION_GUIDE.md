# 组件迁移指南

本文档提供从 Ant Design Vue 迁移到 Element Plus 的详细指南。

## 概述

为了统一UI框架和实现扁平化、紧凑的设计风格，我们将所有组件从 Ant Design Vue 迁移到 Element Plus。

## 组件映射表

### 基础组件

| Ant Design Vue | Element Plus | 说明 |
|----------------|--------------|------|
| `a-button` | `el-button` | 按钮组件 |
| `a-input` | `el-input` | 输入框组件 |
| `a-textarea` | `el-input type="textarea"` | 文本域组件 |
| `a-select` | `el-select` | 选择器组件 |
| `a-select-option` | `el-option` | 选择器选项 |
| `a-radio` | `el-radio` | 单选框 |
| `a-radio-group` | `el-radio-group` | 单选框组 |
| `a-checkbox` | `el-checkbox` | 复选框 |
| `a-switch` | `el-switch` | 开关 |
| `a-input-number` | `el-input-number` | 数字输入框 |

### 布局组件

| Ant Design Vue | Element Plus | 说明 |
|----------------|--------------|------|
| `a-row` | `el-row` | 行布局 |
| `a-col` | `el-col` | 列布局 |
| `a-space` | 使用 CSS Flexbox 或 `el-space` | 间距组件 |
| `a-divider` | `el-divider` | 分割线 |

### 数据展示

| Ant Design Vue | Element Plus | 说明 |
|----------------|--------------|------|
| `a-table` | `el-table` | 表格组件 |
| `a-card` | `el-card` | 卡片组件 |
| `a-tag` | `el-tag` | 标签组件 |
| `a-badge` | `el-badge` | 徽章组件 |
| `a-list` | 自定义列表 + `el-scrollbar` | 列表组件 |
| `a-avatar` | `el-avatar` | 头像组件 |

### 反馈组件

| Ant Design Vue | Element Plus | 说明 |
|----------------|--------------|------|
| `a-modal` | `el-dialog` | 对话框组件 |
| `a-alert` | `el-alert` | 警告提示 |
| `a-message` | `ElMessage` | 消息提示 |
| `a-notification` | `ElNotification` | 通知提醒 |

### 表单组件

| Ant Design Vue | Element Plus | 说明 |
|----------------|--------------|------|
| `a-form` | `el-form` | 表单组件 |
| `a-form-item` | `el-form-item` | 表单项 |

### 导航组件

| Ant Design Vue | Element Plus | 说明 |
|----------------|--------------|------|
| `a-menu` | `el-menu` | 菜单组件 |
| `a-dropdown` | `el-dropdown` | 下拉菜单 |

## 属性和事件映射

### 常见属性差异

#### v-model 绑定

**Ant Design Vue:**
```vue
<a-input v-model:value="inputValue" />
<a-switch v-model:checked="switchValue" />
```

**Element Plus:**
```vue
<el-input v-model="inputValue" />
<el-switch v-model="switchValue" />
```

#### 尺寸属性

**Ant Design Vue:**
```vue
<a-button size="large">按钮</a-button>
<a-input size="small" />
```

**Element Plus:**
```vue
<el-button size="large">按钮</el-button>
<el-input size="small" />
```

#### 类型属性

**Ant Design Vue:**
```vue
<a-button type="primary">主要按钮</a-button>
<a-alert type="success" />
```

**Element Plus:**
```vue
<el-button type="primary">主要按钮</el-button>
<el-alert type="success" />
```

### 事件差异

#### 表单事件

**Ant Design Vue:**
```vue
<a-input @change="handleChange" @blur="handleBlur" />
```

**Element Plus:**
```vue
<el-input @input="handleChange" @blur="handleBlur" />
```

#### 选择器事件

**Ant Design Vue:**
```vue
<a-select @change="handleSelectChange" />
```

**Element Plus:**
```vue
<el-select @change="handleSelectChange" />
```

## 样式迁移

### 使用新的样式系统

我们建立了完整的样式系统，包括：

1. **设计令牌** (`design-tokens.css`)
2. **主题变量** (`theme.css`)
3. **组件样式** (`components.css`)
4. **工具类** (`utilities.css`)

### 工具类使用

**旧的样式:**
```vue
<div class="flex-center">
  <div class="mb-16">内容</div>
</div>
```

**新的样式:**
```vue
<div class="flex-center">
  <div class="mb-lg">内容</div>
</div>
```

### 间距系统

使用新的间距变量：

```css
/* 旧的间距 */
margin-bottom: 16px;
padding: 20px;

/* 新的间距 */
margin-bottom: var(--compact-spacing-lg);
padding: var(--compact-spacing-xl);
```

## 迁移步骤

### 1. 更新导入

**旧的导入:**
```typescript
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
```

**新的导入:**
```typescript
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
```

### 2. 更新组件标签

**旧的组件:**
```vue
<a-modal v-model:visible="visible" title="标题">
  <a-form :model="formData">
    <a-form-item label="名称">
      <a-input v-model:value="formData.name" />
    </a-form-item>
  </a-form>
</a-modal>
```

**新的组件:**
```vue
<el-dialog v-model="visible" title="标题">
  <el-form :model="formData">
    <el-form-item label="名称">
      <el-input v-model="formData.name" />
    </el-form-item>
  </el-form>
</el-dialog>
```

### 3. 更新样式类

**旧的样式:**
```vue
<style scoped>
.custom-card {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  padding: 20px;
}
</style>
```

**新的样式:**
```vue
<style scoped>
.custom-card {
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  padding: var(--compact-spacing-xl);
}
</style>
```

### 4. 更新消息提示

**旧的消息:**
```typescript
message.success('操作成功')
message.error('操作失败')
```

**新的消息:**
```typescript
ElMessage.success('操作成功')
ElMessage.error('操作失败')
```

## 最佳实践

### 1. 使用统一的样式系统

- 优先使用设计令牌中定义的变量
- 使用工具类进行快速布局
- 保持组件样式的一致性

### 2. 响应式设计

```css
/* 使用断点变量 */
@media (max-width: 768px) {
  .component {
    padding: var(--compact-spacing-md);
  }
}
```

### 3. 扁平化设计原则

- 使用较小的圆角值
- 减少阴影效果
- 采用紧凑的间距
- 简化视觉层次

### 4. 组件命名规范

```vue
<!-- 使用语义化的类名 -->
<div class="user-profile-card">
  <div class="user-profile-card__header">
    <h3 class="user-profile-card__title">用户信息</h3>
  </div>
  <div class="user-profile-card__body">
    <!-- 内容 -->
  </div>
</div>
```

## 常见问题

### Q: 如何处理复杂的表格组件？

A: Element Plus 的表格组件功能强大，可以满足大部分需求。对于复杂场景，可以结合自定义组件实现。

### Q: 图标如何迁移？

A: 从 `@ant-design/icons-vue` 迁移到 `@element-plus/icons-vue`，大部分图标都有对应的替代品。

### Q: 如何保持设计一致性？

A: 严格使用我们定义的设计令牌和工具类，避免硬编码样式值。

## 参考资源

- [Element Plus 官方文档](https://element-plus.org/)
- [设计令牌文档](./design-tokens.md)
- [组件样式指南](./component-styles.md)
