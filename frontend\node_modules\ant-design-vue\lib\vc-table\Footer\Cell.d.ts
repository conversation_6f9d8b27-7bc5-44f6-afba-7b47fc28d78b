import type { AlignType } from '../interface';
export interface SummaryCellProps {
    index?: number;
    colSpan?: number;
    rowSpan?: number;
    align?: AlignType;
}
declare const _default: import("vue").DefineComponent<{
    index?: any;
    align?: any;
    colSpan?: any;
    rowSpan?: any;
}, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{
    index?: any;
    align?: any;
    colSpan?: any;
    rowSpan?: any;
}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
