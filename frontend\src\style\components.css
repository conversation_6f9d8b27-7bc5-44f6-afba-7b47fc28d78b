/**
 * 组件样式
 * 定义自定义组件和Element Plus组件的扩展样式
 */

/* ========== 布局组件 ========== */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background-color: var(--color-bg-page);
}

.page-content {
  padding: var(--compact-spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

/* 内容区域 */
.content-wrapper {
  background-color: var(--color-bg-overlay);
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  padding: var(--compact-spacing-xl);
  margin-bottom: var(--compact-spacing-lg);
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--compact-spacing-xl);
  padding-bottom: var(--compact-spacing-lg);
  border-bottom: var(--flat-border);
}

.page-title {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.page-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: var(--compact-spacing-xs) 0 0 0;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
}

/* ========== 卡片组件 ========== */

/* 统计卡片 */
.stat-card {
  background: var(--color-bg-overlay);
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  border: var(--flat-border);
  padding: var(--compact-spacing-lg);
  transition: var(--transition-all);
}

.stat-card:hover {
  box-shadow: var(--flat-shadow-base);
  transform: translateY(-1px);
}

.stat-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--compact-spacing-md);
}

.stat-card__title {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-normal);
  margin: 0;
}

.stat-card__icon {
  width: 32px;
  height: 32px;
  border-radius: var(--flat-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.stat-card__value {
  font-size: var(--font-size-extra-large);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.stat-card__change {
  font-size: var(--font-size-small);
  margin-top: var(--compact-spacing-xs);
}

.stat-card__change--positive {
  color: var(--color-success);
}

.stat-card__change--negative {
  color: var(--color-danger);
}

/* 信息卡片 */
.info-card {
  background: var(--color-bg-overlay);
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  border: var(--flat-border);
  overflow: hidden;
}

.info-card__header {
  padding: var(--compact-spacing-lg);
  background: var(--color-fill-lighter);
  border-bottom: var(--flat-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-card__title {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.info-card__body {
  padding: var(--compact-spacing-lg);
}

.info-card__footer {
  padding: var(--compact-spacing-lg);
  background: var(--color-fill-lighter);
  border-top: var(--flat-border);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--compact-spacing-md);
}

/* ========== 表格组件 ========== */

/* 表格容器 */
.table-container {
  background: var(--color-bg-overlay);
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  border: var(--flat-border);
  overflow: hidden;
}

.table-header {
  padding: var(--compact-spacing-lg);
  background: var(--color-fill-lighter);
  border-bottom: var(--flat-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table-title {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
}

/* 表格工具栏 */
.table-toolbar {
  padding: var(--compact-spacing-lg);
  background: var(--color-fill-light);
  border-bottom: var(--flat-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--compact-spacing-md);
}

.table-toolbar__left {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
  flex: 1;
}

.table-toolbar__right {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
}

/* ========== 表单组件 ========== */

/* 表单容器 */
.form-container {
  background: var(--color-bg-overlay);
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  border: var(--flat-border);
  padding: var(--compact-spacing-xl);
}

.form-section {
  margin-bottom: var(--compact-spacing-xl);
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section__title {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0 0 var(--compact-spacing-lg) 0;
  padding-bottom: var(--compact-spacing-md);
  border-bottom: var(--flat-border);
}

.form-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--compact-spacing-md);
  padding-top: var(--compact-spacing-lg);
  border-top: var(--flat-border);
  margin-top: var(--compact-spacing-xl);
}

/* ========== 状态组件 ========== */

/* 状态标签 */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--compact-spacing-xs);
  padding: var(--compact-spacing-xs) var(--compact-spacing-sm);
  border-radius: var(--flat-border-radius);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  border: var(--flat-border);
}

.status-tag--success {
  background: var(--color-success-extra-light);
  color: var(--color-success);
  border-color: var(--color-success-lighter);
}

.status-tag--warning {
  background: var(--color-warning-extra-light);
  color: var(--color-warning);
  border-color: var(--color-warning-lighter);
}

.status-tag--danger {
  background: var(--color-danger-extra-light);
  color: var(--color-danger);
  border-color: var(--color-danger-lighter);
}

.status-tag--info {
  background: var(--color-info-extra-light);
  color: var(--color-info);
  border-color: var(--color-info-lighter);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* ========== 加载组件 ========== */

/* 加载容器 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--compact-spacing-xl);
  color: var(--color-text-secondary);
}

.loading-text {
  margin-left: var(--compact-spacing-md);
  font-size: var(--font-size-base);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--compact-spacing-5xl);
  color: var(--color-text-secondary);
}

.empty-state__icon {
  font-size: 48px;
  margin-bottom: var(--compact-spacing-lg);
  opacity: 0.5;
}

.empty-state__title {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--compact-spacing-sm) 0;
}

.empty-state__description {
  font-size: var(--font-size-base);
  margin: 0 0 var(--compact-spacing-lg) 0;
  text-align: center;
  max-width: 300px;
}

.empty-state__actions {
  display: flex;
  gap: var(--compact-spacing-md);
}

/* ========== 导航组件 ========== */

/* 面包屑 */
.breadcrumb-container {
  padding: var(--compact-spacing-md) 0;
  margin-bottom: var(--compact-spacing-lg);
}

/* 标签页 */
.tabs-container {
  background: var(--color-bg-overlay);
  border-radius: var(--flat-border-radius-large);
  box-shadow: var(--flat-shadow-light);
  border: var(--flat-border);
  overflow: hidden;
}

.tabs-header {
  background: var(--color-fill-lighter);
  border-bottom: var(--flat-border);
}

.tabs-content {
  padding: var(--compact-spacing-lg);
}

/* ========== 工具提示 ========== */

.tooltip-content {
  max-width: 200px;
  word-wrap: break-word;
}

/* ========== 响应式设计 ========== */

@media (max-width: 768px) {
  .page-content {
    padding: var(--compact-spacing-md);
  }
  
  .content-wrapper {
    padding: var(--compact-spacing-lg);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--compact-spacing-md);
  }
  
  .page-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .table-toolbar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .table-toolbar__left,
  .table-toolbar__right {
    width: 100%;
    justify-content: flex-start;
  }
  
  .form-actions {
    flex-direction: column-reverse;
    align-items: stretch;
  }
  
  .form-actions .el-button {
    width: 100%;
  }
}

/* ========== 打印样式 ========== */

@media print {
  .page-actions,
  .table-actions,
  .form-actions {
    display: none;
  }
  
  .content-wrapper {
    box-shadow: none;
    border: var(--flat-border);
  }
}
