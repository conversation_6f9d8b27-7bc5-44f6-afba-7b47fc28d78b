<template>
  <div class="layout-container">
    <el-container>
      <el-aside :width="sidebarWidth" class="sidebar-container">
        <Sidebar :collapsed="!sidebar.opened" />
      </el-aside>
      <el-container>
        <el-header class="header-container">
          <div class="header-left">
            <el-button
              text
              @click="toggleSidebar"
              class="sidebar-toggle"
            >
              <el-icon size="18"><Fold v-if="sidebar.opened" /><Expand v-else /></el-icon>
            </el-button>
            <div class="header-title">
              <h1 class="system-title">应用项目管理系统</h1>
              <span class="system-subtitle">Application Project Manager</span>
            </div>
          </div>
          <div class="header-right">
            <div class="header-actions">
              <!-- 通知按钮 -->
              <el-badge :value="3" :max="99" class="notification-badge">
                <el-button text circle class="action-button">
                  <el-icon size="18"><Bell /></el-icon>
                </el-button>
              </el-badge>

              <!-- 用户下拉菜单 -->
              <el-dropdown @command="handleCommand" trigger="click">
                <div class="user-info">
                  <el-avatar :size="32" :src="userAvatar" class="user-avatar" />
                  <div class="user-details">
                    <span class="username">{{ user?.full_name || user?.username }}</span>
                    <span class="user-role">管理员</span>
                  </div>
                  <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <el-icon><User /></el-icon>
                      个人信息
                    </el-dropdown-item>
                    <el-dropdown-item command="settings">
                      <el-icon><Setting /></el-icon>
                      系统设置
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        <el-main class="main-container">
          <div class="main-content">
            <router-view />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import Sidebar from './components/Sidebar.vue'
import { Bell, User, Setting, SwitchButton } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const user = computed(() => authStore.user)
const sidebar = computed(() => appStore.sidebar)
const sidebarWidth = computed(() => sidebar.value.opened ? '200px' : '64px')
const userAvatar = computed(() => `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.value?.username || 'default'}`)

const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人信息页面
      router.push('/profile')
      break
    case 'settings':
      // 跳转到设置页面
      router.push('/settings')
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      break
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.sidebar-container {
  background: var(--color-bg-overlay);
  border-right: var(--flat-border);
  transition: width var(--transition-duration-base) var(--transition-function-ease-in-out-bezier);
  box-shadow: var(--flat-shadow-light);
  z-index: var(--z-index-normal);
}

.header-container {
  background: var(--color-bg-overlay);
  border-bottom: var(--flat-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--compact-spacing-xl);
  box-shadow: var(--flat-shadow-light);
  height: var(--header-height);
  position: relative;
  z-index: var(--z-index-top);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-lg);
  flex: 1;
}

.sidebar-toggle {
  padding: var(--compact-spacing-sm);
  border-radius: var(--flat-border-radius);
  color: var(--color-text-secondary);
  transition: var(--transition-all);
}

.sidebar-toggle:hover {
  background-color: var(--color-fill-light);
  color: var(--color-primary);
}

.header-title {
  display: flex;
  flex-direction: column;
  gap: var(--compact-spacing-xs);
}

.system-title {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.system-subtitle {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
}

.notification-badge {
  display: flex;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: var(--flat-border-radius);
  color: var(--color-text-secondary);
  transition: var(--transition-all);
}

.action-button:hover {
  background-color: var(--color-fill-light);
  color: var(--color-primary);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--compact-spacing-md);
  cursor: pointer;
  padding: var(--compact-spacing-sm) var(--compact-spacing-md);
  border-radius: var(--flat-border-radius);
  transition: var(--transition-all);
  border: var(--flat-border);
  border-color: transparent;
}

.user-info:hover {
  background-color: var(--color-fill-light);
  border-color: var(--color-border-light);
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: var(--compact-spacing-xs);
  min-width: 0;
}

.username {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-icon {
  color: var(--color-text-secondary);
  transition: var(--transition-all);
  flex-shrink: 0;
}

.user-info:hover .dropdown-icon {
  color: var(--color-primary);
}

.main-container {
  background: var(--color-bg-page);
  padding: 0;
  overflow: auto;
}

.main-content {
  padding: var(--compact-spacing-lg);
  min-height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 var(--compact-spacing-lg);
  }

  .header-left {
    gap: var(--compact-spacing-md);
  }

  .system-title {
    font-size: var(--font-size-medium);
  }

  .system-subtitle {
    display: none;
  }

  .user-details {
    display: none;
  }

  .main-content {
    padding: var(--compact-spacing-md);
  }
}

@media (max-width: 480px) {
  .header-title {
    display: none;
  }

  .header-actions {
    gap: var(--compact-spacing-sm);
  }
}
</style>