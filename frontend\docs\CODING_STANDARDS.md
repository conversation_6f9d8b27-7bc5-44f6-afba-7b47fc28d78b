# 前端代码规范

本文档定义了项目的前端代码规范和最佳实践。

## 目录结构规范

```
src/
├── api/                    # API 接口
├── components/             # 通用组件
├── composables/           # 组合式函数
├── directives/            # 自定义指令
├── layout/                # 布局组件
├── router/                # 路由配置
├── services/              # 业务服务
├── stores/                # 状态管理
├── style/                 # 样式文件
│   ├── design-tokens.css  # 设计令牌
│   ├── theme.css          # 主题变量
│   ├── components.css     # 组件样式
│   ├── utilities.css      # 工具类
│   └── index.css          # 主样式文件
├── types/                 # 类型定义
├── utils/                 # 工具函数
└── views/                 # 页面组件
```

## Vue 组件规范

### 1. 组件命名

**文件命名：** 使用 PascalCase
```
UserProfile.vue
TaskFormModal.vue
AppSettingsPanel.vue
```

**组件名：** 使用 PascalCase
```vue
<script setup lang="ts">
// 组件名应该与文件名一致
defineOptions({
  name: 'UserProfile'
})
</script>
```

### 2. 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 2. 类型定义
interface Props {
  userId: number
  readonly?: boolean
}

interface Emits {
  (e: 'update', data: UserData): void
  (e: 'delete', id: number): void
}

// 3. Props 和 Emits
const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()

// 4. 响应式数据
const loading = ref(false)
const userData = ref<UserData | null>(null)

// 5. 计算属性
const displayName = computed(() => {
  return userData.value?.fullName || userData.value?.username || '未知用户'
})

// 6. 方法
const loadUserData = async () => {
  // 实现逻辑
}

// 7. 生命周期
onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
/* 样式内容 */
</style>
```

### 3. Props 定义

```typescript
// 使用 TypeScript 接口定义 Props
interface Props {
  // 必需属性
  userId: number
  userName: string
  
  // 可选属性
  avatar?: string
  readonly?: boolean
  
  // 联合类型
  status: 'active' | 'inactive' | 'pending'
  
  // 对象类型
  config: {
    theme: string
    language: string
  }
  
  // 数组类型
  permissions: string[]
}

// 使用默认值
const props = withDefaults(defineProps<Props>(), {
  avatar: '',
  readonly: false,
  config: () => ({
    theme: 'light',
    language: 'zh-CN'
  }),
  permissions: () => []
})
```

### 4. 事件定义

```typescript
// 定义事件类型
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', data: FormData): void
  (e: 'submit', data: FormData): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 触发事件
const handleSubmit = () => {
  emit('submit', formData.value)
}
```

## 样式规范

### 1. 样式组织

```vue
<style scoped>
/* 1. 组件根样式 */
.user-profile {
  display: flex;
  flex-direction: column;
  gap: var(--compact-spacing-lg);
}

/* 2. 子元素样式 */
.user-profile__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--compact-spacing-lg);
  border-bottom: var(--flat-border);
}

.user-profile__title {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
}

.user-profile__body {
  padding: var(--compact-spacing-lg);
}

/* 3. 状态样式 */
.user-profile--loading {
  opacity: 0.6;
  pointer-events: none;
}

.user-profile--readonly .user-profile__actions {
  display: none;
}

/* 4. 响应式样式 */
@media (max-width: 768px) {
  .user-profile__header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--compact-spacing-md);
  }
}
</style>
```

### 2. CSS 类命名规范

使用 BEM (Block Element Modifier) 命名规范：

```css
/* Block */
.user-card { }

/* Element */
.user-card__header { }
.user-card__body { }
.user-card__footer { }

/* Modifier */
.user-card--large { }
.user-card--disabled { }
.user-card__header--sticky { }
```

### 3. 使用设计令牌

```css
/* ✅ 正确：使用设计令牌 */
.component {
  padding: var(--compact-spacing-lg);
  margin-bottom: var(--compact-spacing-xl);
  border-radius: var(--flat-border-radius);
  box-shadow: var(--flat-shadow-light);
  color: var(--color-text-primary);
  background: var(--color-bg-overlay);
}

/* ❌ 错误：硬编码值 */
.component {
  padding: 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #333;
  background: #fff;
}
```

## TypeScript 规范

### 1. 类型定义

```typescript
// 接口定义
interface User {
  id: number
  username: string
  email: string
  avatar?: string
  createdAt: string
  updatedAt: string
}

// 类型别名
type UserStatus = 'active' | 'inactive' | 'pending'
type UserRole = 'admin' | 'user' | 'guest'

// 泛型接口
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 工具类型
type CreateUserData = Omit<User, 'id' | 'createdAt' | 'updatedAt'>
type UpdateUserData = Partial<CreateUserData>
```

### 2. 函数类型

```typescript
// 函数签名
type EventHandler<T = void> = (data: T) => void
type AsyncHandler<T, R> = (data: T) => Promise<R>

// 使用示例
const handleUserUpdate: EventHandler<User> = (user) => {
  console.log('用户更新:', user)
}

const saveUser: AsyncHandler<CreateUserData, User> = async (userData) => {
  const response = await userApi.create(userData)
  return response.data
}
```

## API 调用规范

### 1. API 文件组织

```typescript
// api/user.ts
import { request } from './request'

export interface User {
  id: number
  username: string
  email: string
}

export interface CreateUserData {
  username: string
  email: string
  password: string
}

export const userApi = {
  // 获取用户列表
  getUsers: (params?: {
    page?: number
    size?: number
    search?: string
  }) => {
    return request.get<ApiResponse<User[]>>('/users', { params })
  },

  // 获取用户详情
  getUser: (id: number) => {
    return request.get<ApiResponse<User>>(`/users/${id}`)
  },

  // 创建用户
  createUser: (data: CreateUserData) => {
    return request.post<ApiResponse<User>>('/users', data)
  },

  // 更新用户
  updateUser: (id: number, data: Partial<CreateUserData>) => {
    return request.put<ApiResponse<User>>(`/users/${id}`, data)
  },

  // 删除用户
  deleteUser: (id: number) => {
    return request.delete<ApiResponse<void>>(`/users/${id}`)
  }
}
```

### 2. 错误处理

```typescript
// 在组件中使用
const loadUsers = async () => {
  try {
    loading.value = true
    const response = await userApi.getUsers({
      page: currentPage.value,
      size: pageSize.value
    })
    users.value = response.data.data
  } catch (error: any) {
    ElMessage.error(error.message || '加载用户列表失败')
    console.error('加载用户失败:', error)
  } finally {
    loading.value = false
  }
}
```

## 状态管理规范

### 1. Store 定义

```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import { userApi } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const users = ref<User[]>([])
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!currentUser.value)
  const userCount = computed(() => users.value.length)

  // 方法
  const setCurrentUser = (user: User | null) => {
    currentUser.value = user
  }

  const loadUsers = async () => {
    try {
      loading.value = true
      const response = await userApi.getUsers()
      users.value = response.data.data
    } catch (error) {
      console.error('加载用户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    currentUser.value = null
    // 清理其他状态
  }

  return {
    // 状态
    currentUser,
    users,
    loading,
    // 计算属性
    isLoggedIn,
    userCount,
    // 方法
    setCurrentUser,
    loadUsers,
    logout
  }
})
```

## 工具函数规范

### 1. 函数命名和组织

```typescript
// utils/format.ts
/**
 * 格式化日期
 * @param date 日期对象或字符串
 * @param format 格式化模式
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  date: Date | string,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  // 实现逻辑
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
```

## 测试规范

### 1. 组件测试

```typescript
// tests/components/UserProfile.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('应该正确渲染用户信息', () => {
    const wrapper = mount(UserProfile, {
      props: {
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>'
        }
      }
    })

    expect(wrapper.find('.user-profile__name').text()).toBe('testuser')
    expect(wrapper.find('.user-profile__email').text()).toBe('<EMAIL>')
  })

  it('应该在只读模式下隐藏操作按钮', () => {
    const wrapper = mount(UserProfile, {
      props: {
        user: { id: 1, username: 'test', email: '<EMAIL>' },
        readonly: true
      }
    })

    expect(wrapper.find('.user-profile__actions').exists()).toBe(false)
  })
})
```

## 性能优化规范

### 1. 组件懒加载

```typescript
// router/index.ts
const routes = [
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/users/index.vue')
  }
]
```

### 2. 计算属性缓存

```typescript
// 使用计算属性进行复杂计算
const expensiveValue = computed(() => {
  return heavyCalculation(props.data)
})

// 避免在模板中直接调用函数
// ❌ 错误
// <div>{{ heavyCalculation(data) }}</div>

// ✅ 正确
// <div>{{ expensiveValue }}</div>
```

### 3. 列表渲染优化

```vue
<template>
  <!-- 使用 key 优化列表渲染 -->
  <div v-for="item in items" :key="item.id">
    {{ item.name }}
  </div>
  
  <!-- 大列表使用虚拟滚动 -->
  <el-virtual-list
    :data="largeList"
    :height="400"
    :item-size="50"
  >
    <template #default="{ item }">
      <div>{{ item.name }}</div>
    </template>
  </el-virtual-list>
</template>
```

## 代码审查清单

- [ ] 组件命名符合规范
- [ ] 使用 TypeScript 类型定义
- [ ] Props 和 Emits 正确定义
- [ ] 使用设计令牌而非硬编码值
- [ ] 样式使用 BEM 命名规范
- [ ] 错误处理完善
- [ ] 响应式设计考虑
- [ ] 性能优化措施
- [ ] 代码注释充分
- [ ] 测试覆盖完整
